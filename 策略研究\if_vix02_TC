Params	
	Numeric NT(100);//开仓区间周期参数
	Numeric RS(50);//默认出场参数
	Numeric M(1.5); //出场自适应参数
	Numeric X(3);//自适应参数的步长
	Numeric Length(30);
	Numeric Length2(45); 
	Numeric lon(25);  //均线周期
    Numeric SA(5); 
Vars 
	Series<Numeric> ATRMD;
	Series<Numeric> ATRZZ;
	Series<Numeric> TR;
	Series<Numeric> ATR;
	Series<Numeric> ATRN;
	Series<Numeric> HH;
	Series<Numeric> LL;
	Numeric XY;
	Numeric SY;
	Numeric mids;
	Series<Numeric> TRS;//跟踪止损 
	Series<Numeric> Myprice2;
	Series<Numeric> Myprice3;
	Series<Numeric> liQKA;
	Series<Numeric> DliqPoint;
	Series<Numeric> KliqPoint;
	Series<Numeric> HighAfterEntry;
	Series<Numeric> LowAfterEntry;
	Series<Numeric> barcoutN;
	Series<Numeric> barN;
	Series<Numeric> NN;

	Numeric S;
	Series<Numeric> HHD;
	Series<Numeric> LLD;
	Series<Numeric> MAHD;
	Series<Numeric> MALD;
	Series<Numeric> CD;
	Series<Numeric> MHCD;
	Series<Numeric> MLCD;
	Series<Numeric> H1;
	Series<Numeric> L1;
	Series<Bool> DD;
	Series<Bool> KK;
	Series<Bool> ZD;
	Series<Numeric> VIX;
	Series<Numeric> VIX_index; 
	Series<Numeric> Lots;
	Series<Numeric> Ma1;
Events
	OnInit()
	{
		AddDataFlag(Enum_Data_RolloverBackWard());	//设置后复权

		AddDataFlag(Enum_Data_RolloverRealPrice());	//设置映射真实价格

		AddDataFlag(Enum_Data_AutoSwapPosition());	//设置自动换仓

		AddDataFlag(Enum_Data_IgnoreSwapSignalCalc());	//设置忽略换仓信号计算

		
	}
    onBar(ArrayRef<Integer> indexs)
    {    	
			Ma1=XAverage(C,lon);
			PlotNumeric("Ma1",Ma1);
    		
    		if(CurrentBar==0)
        	{
    			NN=NT;
        		TRS=RS;
        		barN=0;
    			S=X;
        	}
        		//记录开仓后高低点
            If(BarsSinceentry == 0)
            {
                HighAfterEntry = High;
                LowAfterEntry = Low;
            }else
            {
                HighAfterEntry = Min(HighAfterEntry,High); // 空头止损，更新最低的最高价
                LowAfterEntry = Max(LowAfterEntry,Low);    // 多头止损，更新最高的最低价
            }
 		
        	//PlotNumeric("N",N);
        	//PlotNumeric("TRS",TRS);
        	
        	HH=Highest(H,NN)+ATR*S;
    		LL=Lowest(L,NN)-ATR*S;
    		
        	//HH=OpenD(0)+ATR*S;
    		//LL=OpenD(0)-ATR*S;
    		PlotNumeric("HH",HH);
    		PlotNumeric("LL",LL);
    	
		
			if (H>=HH[1] and MarketPosition==0 and Close[1]>Ma1[1]) 
			{
				buy(Lots,max(open,HH[1]));
				LowAfterEntry = max(open,HH[1]);//保存多头开仓价格;
				NN=NT;
				S=SA;
			}
			//if(L<=LL[1]  and MarketPosition==0 and Close[1]<Ma1[1]) 
			//{
			//	SellShort(Lots,min(open,LL[1]));
			//	HighAfterEntry = min(open,LL[1]);//保存空头开仓价格;
			//	NN=NT;
			//	S=SA;
			//}

			if (open>=HH[1] and open[1]<Close[1] and MarketPosition==0 and Close[1]>Ma1[1]) //
			{
				buy(Lots,open);
				LowAfterEntry = open;//保存多头开仓价格;
				NN=NT;
				S=SA;
			}
			//if(open<=LL[1] and open[1]>Close[1] and MarketPosition==0 and Close[1]<Ma1[1]) //
			//{
			//	SellShort(Lots,open);
			//	HighAfterEntry = open;//保存空头开仓价格;
			//	NN=NT;
			//	S=SA;
			//}		

		
    		Commentary("barcoutN"+text(barcoutN));
    		Commentary("BarsSinceEntry"+text(BarsSinceEntry));
        	If(MarketPosition == 0)   // 自适应参数默认值；
        	{
        		liQKA = 1;
        		barcoutN=0;
        	}Else if(BarsSinceEntry>barcoutN)					 //当有持仓的情况下，liQKA会随着持仓时间的增加而逐渐减小，即止损止盈幅度乘数的减少。
        	{
        		liQKA = liQKA - 0.1; 
        		liQKA = Max(liQKA,0.3);
        		barcoutN=BarsSinceEntry;
        	}
        	if(MarketPosition>0)
        	{
        	DliqPoint = LowAfterEntry - (Open*TRS/1000)*liQKA; //经过计算，这根吊灯出场线会随着持仓时间的增加变的越来越敏感；
        	PlotNumeric("DliqPoint",DliqPoint);
        	}
        	if(MarketPosition<0)
        	{
        	KliqPoint = HighAfterEntry + (Open*TRS/1000)*liQKA; //经过计算，这根吊灯出场线会随着持仓时间的增加变的越来越敏感；
        	PlotNumeric("KliqPoint",KliqPoint);
        	}
        	// 画线
        	Commentary("(Open*TRS/1000)*liQKA"+text((Open*TRS/1000)*liQKA));
    
        	// 持有多单时
         	If(MarketPosition >0 And BarsSinceEntry >0  And Low <= DliqPoint[1] and DliqPoint[1]>0 ) 
        	{
        			Sell(0,Min(Open,DliqPoint[1]));
        			barcoutN=0;
    				TRS=RS;
        	}
        		// 持有空单时
        	If(MarketPosition <0 And BarsSinceEntry >0  And High >= KliqPoint[1] and KliqPoint[1]>0 )
        	{		
        			BuyToCover(0,Max(Open,KliqPoint[1]));
        			barcoutN=0;
    				TRS=RS;
        	}	
       
    }

//------------------------------------------------------------------------
// 编译版本	GS2015.12.25
// 用户版本	2019/06/21 18:51:22
// 版权所有	mujinlong1999
// 更改声明	TradeBlazer Software保留对TradeBlazer平台
//			每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------