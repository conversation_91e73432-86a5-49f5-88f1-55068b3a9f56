//------------------------------------------------------------------------
// 简称: JJP_4_Long
// 名称: 
// 类别: 公式应用
// 类型: 用户应用
// 输出: Void
//------------------------------------------------------------------------
Params
	//此处添加参数
  
	Numeric multi(2);
	Numeric daily_factor(2);
	//Numeric NATRstop(2);
	//Numeric sec_NATRstop(2);
	Numeric STbar(200);
	Numeric Longshort_stop(10);//默认止损止盈的比例
	Numeric lots(2); 


Vars
	//此处添加变量
		Numeric fast(2);
	Numeric slow(30);
	Numeric period(20);  
	Numeric fastest;
	Numeric slowest;
	Numeric dir;
	Numeric volSum(0);
	Series<Numeric>barMove;
	Numeric i;
	Numeric er;
	Numeric cc;
	Numeric sc;
	 Series<Numeric> ret;
	Series<Numeric> atr;
	Series<Numeric> up;
	Series<Numeric> down;
	Series<Numeric> LastRange;
	Series<Numeric> down2;
	Series<Numeric>  up2;
	Series<bool> long_con1;
	Series<bool> long_con2;
		Series<bool> short_con1;
	Series<bool> short_con2;
		Series<Numeric> HighestAfterEntry;   // 开仓后出现的最高价
	Series<Numeric> LowestAfterEntry;    // 开仓后出现的最低价
	Series<Numeric> entry_price;
	Series<Numeric> long_stop;
	Series<bool> long_exit;
		Series<Numeric> short_stop;
	Series<bool> short_exit;
		Series<Numeric> Tup;
	Series<Numeric> Tdown;
	Series<Numeric> Trend;
	Series<Numeric> Trailingsl;
		Series<Numeric> HH;
	Series<Numeric> LL;
	Series<Numeric> HighAfterEntry;
	Series<Numeric> LowAfterEntry;
	Series<Numeric> Entprice;//保存进场价格
	Series<Numeric> Barct(0);
	Numeric Wi;
	Numeric Vwapprice;
	Series<Numeric> Vwap; 
	Series<Numeric> Wisum;
	Series<Numeric> Vwapline;
	Series<Numeric> VWAP_A;
Events
	//此处实现事件函数
	
	//初始化事件函数，策略运行期间，首先运行且只有一次
	OnInit()
	{
		//=========数据源相关设置==============
		//AddDataFlag(Enum_Data_RolloverBackWard());	//设置后复权

		AddDataFlag(Enum_Data_RolloverRealPrice());	//设置映射真实价格

		AddDataFlag(Enum_Data_AutoSwapPosition());	//设置自动换仓

	}


	//Bar更新事件函数，参数indexs表示变化的数据源图层ID数组
	OnBar(ArrayRef<Integer> indexs)
	{
		
		If(BarsSinceentry == 0)
        {
            HighAfterEntry = High;
            LowAfterEntry = Low;
        }
        else
        {
            HighAfterEntry = Min(HighAfterEntry,High); // 空头止损，更新最低的最高价
            LowAfterEntry = Max(LowAfterEntry,Low);    // 多头止损，更新最高的最低价
        }
        Commentary("BarsSinceentry"+Text(BarsSinceentry));
		fastest = 2 / (fast + 1);
        slowest = 2 / (slow + 1);
        dir = abs(close - close[period]);
        barMove = Abs(close - close[1]);
       
        If(CurrentBar >= period)
        {	volSum = 0;
			for  i = 0 To period - 1
			{
				volSum = volSum + barMove[i];
			}
			
			If(volSum == 0)
			{
				er = 0;
			} 
			else 
			{
				er = dir / volSum;
			}
			cc = er * (fastest - slowest) + slowest;
			sc = cc^2;
			
			If(CurrentBar == period)
			{
				ret = close[1] + sc * (close - close[1]);
			}
			else 
			{
				ret = ret[1] + sc * (close - ret[1]);
			}
        }
        Commentary("barmove"+Text(barmove));
        PlotNumeric("volSum",volSum);
       // PlotNumeric("ret",ret,ret,white);
        
        atr = Highest(AvgTrueRange(14), 10);
		up = ret - (multi * atr);
		down = ret + (multi * atr);
		
		Tup = IIF(ret[1]>Tup[1],Max(up, Tup[1]), up);
		Tdown = IIF(ret[1]<Tdown[1], Min(down, Tdown[1]), down);
		trend = IIF(ret>Tdown[1], 1, IIF(ret<Tup[1],-1,trend[1]));
		if (trend==1)
		{
			Trailingsl = Tup[1];
			//PlotNumeric("Trailingsl",Trailingsl,Trailingsl,red);
		}
		Else
		{
			Trailingsl = Tdown[1];
			//PlotNumeric("Trailingsl",Trailingsl,Trailingsl,Green);
		}
		
		HH=OpenD(0)+atr*daily_factor;
    	//LL=OpenD(0)-atr*daily_factor;
    	//PlotNumeric("HH",HH);
		
		long_con1 = close>Trailingsl;
		
		if ( trend[1]==1 and long_con1[1] and High>=HH[1] and MarketPosition<>1)
		{
			entry_price = max(Open, HH[1]);
			Buy(lots, entry_price);
			LowAfterEntry=Entry_Price; //保存开多价格
			Barct=1;//移动bar线的计数
		}
		
		/*short_con1 = close<Trailingsl;
		if (trend[1]==-1 and short_con1[1] and Low<=LL[1] and MarketPosition<>-1)
		{
			entry_price = Min(Open, LL[1]);
			SellShort(1, entry_price);
			HighAfterEntry=Entry_Price;//保存开空价格
			Barct=1;//移动bar线的计数
		}*/
		
		// /**************************************************出场**************************************************************
		/*if ( BarsSinceEntry == 0 )     // 开仓后第一个bar数据，直接等于开仓价
    	{
    		HighestAfterEntry = entry_price;
    		LowestAfterEntry = entry_price;
    	}
    	Else If( BarsSinceEntry > 0 )  // 之后的bar数据，不断用最高和最低价做比对，赋值给开仓后最高最低价
    	{
    		HighestAfterEntry = Max(HighestAfterEntry[1],high[1]); 
    		LowestAfterEntry = Min(LowestAfterEntry[1],low[1]);
    	}
    	Else                          // 没有仓位时，保持上次价格信息，没有其他用途
    	{
    		HighestAfterEntry = HighestAfterEntry[1];
    		LowestAfterEntry = LowestAfterEntry[1];
    	}*/
		
		// 第一种出场模块
    /*	If( MarketPosition == 1 && BarsSinceEntry > 1 
    	&& HighestAfterEntry - Entry_Price > NATRstop*atr[1] ) // 有多仓
    	{
			If(low <= HighestAfterEntry - NATRstop*atr[1] ) 
			{
				Sell(0,Min(Open, HighestAfterEntry - NATRstop*atr[1]  ) );
				
				PlotString ("TrailingStop","TrailingStop",high*1.01,White);
			}
		}
    */
    
		/*if(MarketPosition == -1 && BarsSinceEntry > 1 
		&& Entry_Price - LowestAfterEntry > fir_NATRstop*atr[1] ) // 有空仓
		{
			If(high >= LowestAfterEntry + sec_NATRstop*atr[1]  )
			{
				BuyToCover(0,Max(Open,LowestAfterEntry + sec_NATRstop*atr[1] ) );
				PlotString ("TrailingStop","TrailingStop",high*1.01,White);
			}
		}*/
    	 
    	// 第二种出场模块
		//小于STbar，Entprice不加速，采用移动高低点作为出场线
    	If(Barct<=STbar)
    	{
    		Entprice=IIF(MarketPosition>0,LowAfterEntry,IIF(MarketPosition<0,HighAfterEntry,0));
    	}
    	//VWAP移动出场核心计算
    	If(MarketPosition<>0)
    	{
    		Barct=Barct+1;
    		//计算成交量加权成本线
    		Vwapprice = Entprice * IIF(MarketPosition>0, 1-Longshort_stop*0.001, IIF(MarketPosition<0, 1+Longshort_stop*0.001, 0)); 
    		Wi = Vol[1];
    		Wisum = Wisum + Wi;
    		Vwap = (Wi*Vwapprice)+Vwap;
    		Vwapline = Vwap/Wisum;
    		VWAP_A = Vwapline;
    		if(VWAP_A>0)
				//PlotNumeric("VWAP_A",VWAP_A);
    		
    		if(MarketPosition>0 and L<=VWAP_A[1] and VWAP_A[1]>0)
    		{
				Sell(0,Min(open,VWAP_A[1]));
				Wisum=0;
				Vwapprice=0;
				Vwapline=0;
				VWAP_A=0;
				Vwap=0;
				Barct=0;
    		}
    		if(MarketPosition<0 and H>=VWAP_A[1] and VWAP_A[1]>0)
    		{
    				BuyToCover(0,Max(open,VWAP_A[1]));
    				Wisum=0;
    				Vwapprice=0;
    				Vwapline=0;
    				VWAP_A=0;
    				Vwap=0;
    				Barct=0;
    		}
    		//大于STbar，Entprice加速，采用最新的高价或者低价替换Entprice.
    		If(Barct>STbar)
    		{
				Commentary("beyond"+text(Barct));
    			Entprice=IIF(MarketPosition>0,Max(Entprice[1],L[1]),IIF(MarketPosition<0,Min(H[1],Entprice[1]),0));
    			Wisum=0;
    			Vwap=0;
    			Vwapline=0;
    		}
    		Commentary("Barct"+Text(Barct));
    		//PlotNumeric("Entprice",Entprice);
    	}
    	

	}




//------------------------------------------------------------------------
// 编译版本	2022/09/21 084433
// 版权所有	hitlerls
// 更改声明	TradeBlazer Software保留对TradeBlazer平台
//			每一版本的TradeBlazer公式修改和重写的权利
//------------------------------------------------------------------------